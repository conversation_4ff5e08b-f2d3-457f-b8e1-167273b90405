/**
 * 通知队列处理服务
 * 处理数据库触发器产生的通知任务
 */

const { query } = require('../config/database');
const SubscriptionService = require('./subscriptionService');

class NotificationQueueService {
    constructor() {
        this.subscriptionService = new SubscriptionService();
        this.isProcessing = false;
        this.processInterval = 5000; // 5秒检查一次
        this.maxRetryCount = 3; // 最大重试次数
    }

    /**
     * 启动通知队列处理
     */
    start() {
        console.log('🚀 启动通知队列处理服务...');
        this.processQueue();
        
        // 设置定时处理
        setInterval(() => {
            this.processQueue();
        }, this.processInterval);
    }

    /**
     * 处理通知队列
     */
    async processQueue() {
        if (this.isProcessing) {
            return;
        }

        this.isProcessing = true;

        try {
            // 获取待处理的通知任务
            const pendingNotifications = await this.getPendingNotifications();
            
            if (pendingNotifications.length === 0) {
                return;
            }

            console.log(`📋 发现 ${pendingNotifications.length} 个待处理通知任务`);

            // 逐个处理通知任务
            for (const notification of pendingNotifications) {
                await this.processNotification(notification);
            }

        } catch (error) {
            console.error('❌ 处理通知队列异常:', error.message);
        } finally {
            this.isProcessing = false;
        }
    }

    /**
     * 获取待处理的通知任务
     * @returns {Promise<Array>} 通知任务列表
     */
    async getPendingNotifications() {
        try {
            const sql = `
                SELECT id, order_no, factory_name, notification_type, retry_count
                FROM notification_queue
                WHERE status = 'pending' AND retry_count < ?
                ORDER BY created_at ASC
                LIMIT 10
            `;
            
            return await query(sql, [this.maxRetryCount]);
        } catch (error) {
            console.error('❌ 获取待处理通知任务失败:', error.message);
            return [];
        }
    }

    /**
     * 处理单个通知任务
     * @param {Object} notification 通知任务
     */
    async processNotification(notification) {
        const { id, order_no, factory_name, notification_type } = notification;
        
        try {
            console.log(`📤 处理通知任务: ${notification_type} - ${order_no} - ${factory_name}`);
            
            // 更新状态为处理中
            await this.updateNotificationStatus(id, 'processing');
            
            let result;
            
            // 根据通知类型调用相应的发送方法
            switch (notification_type) {
                case 'order_created':
                    result = await this.subscriptionService.sendOrderCreatedNotification(order_no, factory_name);
                    break;
                case 'order_shipped':
                    // 可以扩展其他类型的通知
                    result = await this.subscriptionService.sendOrderImageUploadNotification(order_no, factory_name);
                    break;
                default:
                    throw new Error(`未知的通知类型: ${notification_type}`);
            }
            
            if (result.success) {
                // 发送成功，标记为完成
                await this.updateNotificationStatus(id, 'completed');
                console.log(`✅ 通知发送成功: ${order_no}`);
            } else {
                // 发送失败，增加重试次数
                await this.incrementRetryCount(id, result.message || '发送失败');
                console.log(`⚠️ 通知发送失败: ${order_no} - ${result.message}`);
            }
            
        } catch (error) {
            console.error(`❌ 处理通知任务异常: ${order_no}`, error.message);
            
            // 增加重试次数
            await this.incrementRetryCount(id, error.message);
        }
    }

    /**
     * 更新通知状态
     * @param {number} id 通知ID
     * @param {string} status 状态
     */
    async updateNotificationStatus(id, status) {
        try {
            const sql = `
                UPDATE notification_queue 
                SET status = ?, updated_at = NOW()
                WHERE id = ?
            `;
            
            await query(sql, [status, id]);
        } catch (error) {
            console.error('❌ 更新通知状态失败:', error.message);
        }
    }

    /**
     * 增加重试次数
     * @param {number} id 通知ID
     * @param {string} errorMsg 错误信息
     */
    async incrementRetryCount(id, errorMsg) {
        try {
            const sql = `
                UPDATE notification_queue 
                SET retry_count = retry_count + 1, 
                    error_msg = ?,
                    status = CASE 
                        WHEN retry_count + 1 >= ? THEN 'failed' 
                        ELSE 'pending' 
                    END,
                    updated_at = NOW()
                WHERE id = ?
            `;
            
            await query(sql, [errorMsg, this.maxRetryCount, id]);
        } catch (error) {
            console.error('❌ 增加重试次数失败:', error.message);
        }
    }

    /**
     * 清理已完成的通知任务（保留7天）
     */
    async cleanupCompletedNotifications() {
        try {
            const sql = `
                DELETE FROM notification_queue 
                WHERE status IN ('completed', 'failed') 
                AND created_at < DATE_SUB(NOW(), INTERVAL 7 DAY)
            `;
            
            const result = await query(sql);
            
            if (result.affectedRows > 0) {
                console.log(`🧹 清理了 ${result.affectedRows} 个已完成的通知任务`);
            }
        } catch (error) {
            console.error('❌ 清理通知任务失败:', error.message);
        }
    }

    /**
     * 获取队列统计信息
     * @returns {Promise<Object>} 统计信息
     */
    async getQueueStats() {
        try {
            const sql = `
                SELECT 
                    status,
                    COUNT(*) as count
                FROM notification_queue 
                WHERE created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
                GROUP BY status
            `;
            
            const result = await query(sql);
            
            const stats = {
                pending: 0,
                processing: 0,
                completed: 0,
                failed: 0
            };
            
            result.forEach(row => {
                stats[row.status] = row.count;
            });
            
            return stats;
        } catch (error) {
            console.error('❌ 获取队列统计信息失败:', error.message);
            return null;
        }
    }
}

module.exports = NotificationQueueService;
