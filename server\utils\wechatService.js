/**
 * 微信小程序服务类
 * 处理微信API相关功能，包括获取access_token、openid、发送订阅消息等
 */

const axios = require('axios');
const { query } = require('../config/database');

class WechatService {
    constructor() {
        this.appId = 'wx803f25e0b6de779b';
        this.appSecret = 'effe9d3b781457b6877b9c7faf175fd7';
        this.accessToken = null;
        this.tokenExpireTime = 0;
        this.templateId = 'pe6RVTNTaFPvBv3BGU-mYi8VV9agbvsc27aGpQtR9c8'; // 订单进度提醒模板
    }

    /**
     * 获取access_token
     * @returns {Promise<string>} access_token
     */
    async getAccessToken() {
        const now = Date.now();

        // 如果token还有效，直接返回
        if (this.accessToken && now < this.tokenExpireTime) {
            return this.accessToken;
        }

        try {
            console.log('🔄 正在获取微信access_token...');

            const response = await axios.get(
                `https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=${this.appId}&secret=${this.appSecret}`,
                {
                    timeout: 10000,
                    // 确保使用HTTPS协议
                    httpsAgent: new (require('https').Agent)({
                        rejectUnauthorized: true
                    })
                }
            );

            if (response.data.access_token) {
                this.accessToken = response.data.access_token;
                // 提前5分钟过期，确保安全
                this.tokenExpireTime = now + (response.data.expires_in - 300) * 1000;

                console.log('✅ 获取access_token成功，有效期:', response.data.expires_in, '秒');
                return this.accessToken;
            } else {
                throw new Error(`获取access_token失败: ${response.data.errmsg || '未知错误'}`);
            }
        } catch (error) {
            console.error('❌ 获取access_token失败:', error.message);
            throw error;
        }
    }

    /**
     * 通过code获取用户openid
     * @param {string} code 微信登录code
     * @returns {Promise<string>} openid
     */
    async getOpenidByCode(code) {
        try {
            console.log('🔄 正在获取用户openid...');

            const response = await axios.get(
                `https://api.weixin.qq.com/sns/jscode2session?appid=${this.appId}&secret=${this.appSecret}&js_code=${code}&grant_type=authorization_code`,
                {
                    timeout: 10000,
                    // 确保使用HTTPS协议
                    httpsAgent: new (require('https').Agent)({
                        rejectUnauthorized: true
                    })
                }
            );

            if (response.data.openid) {
                console.log('✅ 获取openid成功');
                return response.data.openid;
            } else {
                throw new Error(`获取openid失败: ${response.data.errmsg || '未知错误'}`);
            }
        } catch (error) {
            console.error('❌ 获取openid失败:', error.message);
            throw error;
        }
    }

    /**
     * 发送订阅消息
     * @param {string} openid 用户openid
     * @param {Object} data 消息数据
     * @param {string} page 跳转页面
     * @returns {Promise<Object>} 发送结果
     */
    async sendSubscribeMessage(openid, data, page = 'pages/imageManage/imageManage') {
        try {
            const accessToken = await this.getAccessToken();
            
            const messageData = {
                touser: openid,
                template_id: this.templateId,
                page: page,
                miniprogram_state: 'formal', // 正式版
                lang: 'zh_CN',
                data: data
            };

            console.log('📤 正在发送订阅消息:', {
                openid: openid.substring(0, 10) + '...',
                template_id: this.templateId,
                page: page
            });

            const response = await axios.post(
                `https://api.weixin.qq.com/cgi-bin/message/subscribe/send?access_token=${accessToken}`,
                messageData,
                {
                    timeout: 10000,
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    // 确保使用HTTPS协议
                    httpsAgent: new (require('https').Agent)({
                        rejectUnauthorized: true
                    })
                }
            );

            // 记录发送日志
            await this.logMessage(openid, this.templateId, messageData, response.data);
            
            if (response.data.errcode === 0) {
                console.log('✅ 订阅消息发送成功');
                return { success: true, data: response.data };
            } else {
                console.error('❌ 订阅消息发送失败:', response.data);
                return { success: false, error: response.data };
            }
            
        } catch (error) {
            console.error('❌ 发送订阅消息异常:', error.message);
            
            // 记录错误日志
            await this.logMessage(openid, this.templateId, null, {
                errcode: -1,
                errmsg: error.message
            });
            
            return { success: false, error: error.message };
        }
    }

    /**
     * 记录消息发送日志
     * @param {string} openid 用户openid
     * @param {string} templateId 模板ID
     * @param {Object} messageData 消息数据
     * @param {Object} result 发送结果
     */
    async logMessage(openid, templateId, messageData, result) {
        try {
            const sql = `
                INSERT INTO message_logs (openid, template_id, message_content, send_status, error_msg, send_time)
                VALUES (?, ?, ?, ?, ?, NOW())
            `;
            
            const status = result.errcode === 0 ? 1 : 2; // 1:成功 2:失败
            const errorMsg = result.errcode !== 0 ? result.errmsg : null;
            const content = messageData ? JSON.stringify(messageData) : null;
            
            await query(sql, [
                openid,
                templateId,
                content,
                status,
                errorMsg
            ]);
            
            console.log('📝 消息发送日志已记录');
        } catch (error) {
            console.error('❌ 记录消息日志失败:', error.message);
        }
    }
}

module.exports = WechatService;
