-- 部署订单生成通知系统
-- 包含触发器、通知队列表和相关配置

USE identify;

-- 1. 创建通知队列表（如果不存在）
CREATE TABLE IF NOT EXISTS notification_queue (
    id INT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    order_no VARCHAR(100) NOT NULL COMMENT '订单号',
    factory_name VARCHAR(100) NOT NULL COMMENT '工厂名称',
    notification_type VARCHAR(50) NOT NULL COMMENT '通知类型：order_created, order_shipped等',
    status VARCHAR(20) DEFAULT 'pending' COMMENT '状态：pending, processing, completed, failed',
    retry_count INT DEFAULT 0 COMMENT '重试次数',
    error_msg TEXT COMMENT '错误信息',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_status (status),
    INDEX idx_order_no (order_no),
    INDEX idx_factory_name (factory_name),
    INDEX idx_notification_type (notification_type),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='通知队列表';

-- 2. 确保订阅相关表存在
CREATE TABLE IF NOT EXISTS user_subscriptions (
    id INT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    openid VARCHAR(100) NOT NULL COMMENT '用户openid',
    template_id VARCHAR(100) NOT NULL COMMENT '模板ID',
    username VARCHAR(50) COMMENT '用户名',
    factory_name VARCHAR(100) COMMENT '工厂名称',
    subscribe_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '订阅时间',
    status TINYINT DEFAULT 1 COMMENT '订阅状态：1=已订阅，0=已取消',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_openid (openid),
    INDEX idx_template_id (template_id),
    INDEX idx_username (username),
    INDEX idx_factory_name (factory_name),
    INDEX idx_status (status),
    UNIQUE KEY uk_openid_template (openid, template_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户订阅表';

CREATE TABLE IF NOT EXISTS message_logs (
    id INT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    openid VARCHAR(100) NOT NULL COMMENT '用户openid',
    template_id VARCHAR(100) NOT NULL COMMENT '模板ID',
    message_content TEXT COMMENT '消息内容JSON',
    send_status TINYINT DEFAULT 0 COMMENT '发送状态：0=发送中，1=成功，2=失败',
    error_msg VARCHAR(500) COMMENT '错误信息',
    send_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '发送时间',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    INDEX idx_openid (openid),
    INDEX idx_template_id (template_id),
    INDEX idx_send_status (send_status),
    INDEX idx_send_time (send_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='消息发送记录表';

-- 3. 删除已存在的触发器（如果有）
DROP TRIGGER IF EXISTS tr_shipping_detail_insert;

-- 4. 创建触发器：当shipping_detail表有新增数据时触发
DELIMITER $$

CREATE TRIGGER tr_shipping_detail_insert
    AFTER INSERT ON shipping_detail
    FOR EACH ROW
BEGIN
    -- 声明变量
    DECLARE notification_count INT DEFAULT 0;
    DECLARE user_subscription_count INT DEFAULT 0;
    
    -- 检查是否已经发送过该订单的生成通知
    SELECT COUNT(*) INTO notification_count
    FROM message_logs 
    WHERE template_id = 'pe6RVTNTaFPvBv3BGU-mYi8VV9agbvsc27aGpQtR9c8' 
    AND message_content LIKE CONCAT('%"character_string1":{"value":"', NEW.order_no, '_created"}%')
    AND send_status = 1;
    
    -- 检查是否有对应工厂的订阅用户
    SELECT COUNT(*) INTO user_subscription_count
    FROM user_subscriptions 
    WHERE factory_name = NEW.receiver 
    AND template_id = 'pe6RVTNTaFPvBv3BGU-mYi8VV9agbvsc27aGpQtR9c8' 
    AND status = 1;
    
    -- 如果没有发送过通知且有订阅用户，则插入通知任务
    IF notification_count = 0 AND user_subscription_count > 0 THEN
        -- 插入通知任务到队列表
        INSERT INTO notification_queue (
            order_no, 
            factory_name, 
            notification_type, 
            status, 
            created_at
        ) VALUES (
            NEW.order_no, 
            NEW.receiver, 
            'order_created', 
            'pending', 
            NOW()
        );
    END IF;
    
END$$

DELIMITER ;

-- 5. 验证部署结果
SELECT 'notification_queue表' as table_name, COUNT(*) as record_count FROM notification_queue
UNION ALL
SELECT 'user_subscriptions表' as table_name, COUNT(*) as record_count FROM user_subscriptions
UNION ALL
SELECT 'message_logs表' as table_name, COUNT(*) as record_count FROM message_logs;

-- 6. 查看触发器
SHOW TRIGGERS LIKE 'shipping_detail';

-- 7. 查看表结构
DESCRIBE notification_queue;
DESCRIBE user_subscriptions;
DESCRIBE message_logs;

-- 8. 插入测试订阅用户（可选）
-- INSERT INTO user_subscriptions (openid, template_id, username, factory_name, status) 
-- VALUES ('test_openid_001', 'pe6RVTNTaFPvBv3BGU-mYi8VV9agbvsc27aGpQtR9c8', 'test_user', '测试工厂', 1);

-- 9. 测试触发器（可选）
-- INSERT INTO shipping_detail (order_no, receiver, follower, notice_date, deliver_company, product_name, spec, width, weight, quantity, remark)
-- VALUES ('TEST_ORDER_TRIGGER_001', '测试工厂', '测试处理人', NOW(), '测试物流', '测试产品', '测试规格', 100, 50, 10, '触发器测试');

-- 10. 查看通知队列（测试后）
-- SELECT * FROM notification_queue ORDER BY created_at DESC LIMIT 5;

COMMIT;
