/**
 * 通知管理路由
 * 处理订阅消息通知相关的API请求
 */

const express = require('express');
const router = express.Router();
const { query } = require('../config/database');
const NotificationQueueService = require('../utils/notificationQueueService');
const SubscriptionService = require('../utils/subscriptionService');

// 创建服务实例
const notificationQueueService = new NotificationQueueService();
const subscriptionService = new SubscriptionService();

/**
 * 获取通知队列统计信息
 * GET /api/notifications/stats
 */
router.get('/stats', async (req, res) => {
    try {
        const stats = await notificationQueueService.getQueueStats();
        
        if (stats) {
            res.json({
                success: true,
                data: stats
            });
        } else {
            res.status(500).json({
                success: false,
                message: '获取统计信息失败'
            });
        }
    } catch (error) {
        console.error('❌ 获取通知队列统计失败:', error);
        res.status(500).json({
            success: false,
            message: '服务器内部错误'
        });
    }
});

/**
 * 获取通知队列列表
 * GET /api/notifications/queue
 */
router.get('/queue', async (req, res) => {
    try {
        const { status = 'all', limit = 20, offset = 0 } = req.query;
        
        let whereClause = '';
        let queryParams = [];
        
        if (status !== 'all') {
            whereClause = 'WHERE status = ?';
            queryParams.push(status);
        }
        
        const sql = `
            SELECT id, order_no, factory_name, notification_type, status, 
                   retry_count, error_msg, created_at, updated_at
            FROM notification_queue
            ${whereClause}
            ORDER BY created_at DESC
            LIMIT ? OFFSET ?
        `;
        
        queryParams.push(parseInt(limit), parseInt(offset));
        
        const notifications = await query(sql, queryParams);
        
        // 获取总数
        const countSql = `
            SELECT COUNT(*) as total
            FROM notification_queue
            ${whereClause}
        `;
        
        const countParams = status !== 'all' ? [status] : [];
        const countResult = await query(countSql, countParams);
        const total = countResult[0].total;
        
        res.json({
            success: true,
            data: {
                notifications,
                total,
                limit: parseInt(limit),
                offset: parseInt(offset)
            }
        });
        
    } catch (error) {
        console.error('❌ 获取通知队列列表失败:', error);
        res.status(500).json({
            success: false,
            message: '服务器内部错误'
        });
    }
});

/**
 * 手动重试失败的通知
 * POST /api/notifications/retry/:id
 */
router.post('/retry/:id', async (req, res) => {
    try {
        const { id } = req.params;
        
        // 获取通知信息
        const sql = `
            SELECT id, order_no, factory_name, notification_type, retry_count
            FROM notification_queue
            WHERE id = ? AND status IN ('failed', 'pending')
        `;
        
        const notifications = await query(sql, [id]);
        
        if (notifications.length === 0) {
            return res.status(404).json({
                success: false,
                message: '通知不存在或状态不允许重试'
            });
        }
        
        const notification = notifications[0];
        
        // 重置状态为pending
        const updateSql = `
            UPDATE notification_queue 
            SET status = 'pending', error_msg = NULL, updated_at = NOW()
            WHERE id = ?
        `;
        
        await query(updateSql, [id]);
        
        // 手动处理通知
        await notificationQueueService.processNotification(notification);
        
        res.json({
            success: true,
            message: '重试请求已提交'
        });
        
    } catch (error) {
        console.error('❌ 重试通知失败:', error);
        res.status(500).json({
            success: false,
            message: '服务器内部错误'
        });
    }
});

/**
 * 手动发送订单生成通知
 * POST /api/notifications/send-order-created
 */
router.post('/send-order-created', async (req, res) => {
    try {
        const { orderNo, factoryName } = req.body;
        
        if (!orderNo || !factoryName) {
            return res.status(400).json({
                success: false,
                message: '订单号和工厂名称不能为空'
            });
        }
        
        const result = await subscriptionService.sendOrderCreatedNotification(orderNo, factoryName);
        
        res.json(result);
        
    } catch (error) {
        console.error('❌ 手动发送订单生成通知失败:', error);
        res.status(500).json({
            success: false,
            message: '服务器内部错误'
        });
    }
});

/**
 * 清理已完成的通知
 * POST /api/notifications/cleanup
 */
router.post('/cleanup', async (req, res) => {
    try {
        await notificationQueueService.cleanupCompletedNotifications();
        
        res.json({
            success: true,
            message: '清理任务已执行'
        });
        
    } catch (error) {
        console.error('❌ 清理通知失败:', error);
        res.status(500).json({
            success: false,
            message: '服务器内部错误'
        });
    }
});

/**
 * 删除指定通知
 * DELETE /api/notifications/:id
 */
router.delete('/:id', async (req, res) => {
    try {
        const { id } = req.params;
        
        const sql = `DELETE FROM notification_queue WHERE id = ?`;
        const result = await query(sql, [id]);
        
        if (result.affectedRows > 0) {
            res.json({
                success: true,
                message: '通知已删除'
            });
        } else {
            res.status(404).json({
                success: false,
                message: '通知不存在'
            });
        }
        
    } catch (error) {
        console.error('❌ 删除通知失败:', error);
        res.status(500).json({
            success: false,
            message: '服务器内部错误'
        });
    }
});

module.exports = router;
