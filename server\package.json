{"name": "image-recognition-server", "version": "1.0.0", "description": "图片文字识别小程序后端服务", "main": "app.js", "scripts": {"start": "node app.js", "dev": "nodemon app.js", "dev:local": "node -r dotenv/config app.js dotenv_config_path=.env.local", "dev:local:watch": "nodemon -r dotenv/config app.js dotenv_config_path=.env.local", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["ocr", "image-recognition", "express", "baidu-api"], "author": "Your Name", "license": "MIT", "dependencies": {"axios": "^1.11.0", "compression": "^1.8.1", "cors": "^2.8.5", "dotenv": "^17.2.0", "express": "^4.18.2", "express-rate-limit": "^7.5.1", "form-data": "^4.0.3", "helmet": "^8.1.0", "ioredis": "^5.6.1", "multer": "^1.4.5-lts.1", "mysql2": "^3.14.2", "node-cron": "^3.0.3", "p-limit": "^6.2.0", "p-queue": "^8.1.0", "redis": "^5.6.0", "sharp": "^0.34.3", "winston": "^3.17.0", "winston-daily-rotate-file": "^5.0.0"}, "devDependencies": {"jest": "^30.0.4", "nodemon": "^3.0.1", "supertest": "^7.1.3"}, "engines": {"node": ">=14.0.0"}}