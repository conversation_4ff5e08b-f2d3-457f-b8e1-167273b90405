-- 创建shipping_detail表新增数据触发器
-- 用于自动发送订单生成通知

USE identify;

-- 删除已存在的触发器（如果有）
DROP TRIGGER IF EXISTS tr_shipping_detail_insert;

-- 创建触发器：当shipping_detail表有新增数据时触发
DELIMITER $$

CREATE TRIGGER tr_shipping_detail_insert
    AFTER INSERT ON shipping_detail
    FOR EACH ROW
BEGIN
    -- 声明变量
    DECLARE notification_count INT DEFAULT 0;
    DECLARE user_subscription_count INT DEFAULT 0;
    
    -- 检查是否已经发送过该订单的生成通知
    SELECT COUNT(*) INTO notification_count
    FROM message_logs 
    WHERE template_id = 'pe6RVTNTaFPvBv3BGU-mYi8VV9agbvsc27aGpQtR9c8' 
    AND message_content LIKE CONCAT('%"character_string1":{"value":"', NEW.order_no, '_created"}%')
    AND send_status = 1;
    
    -- 检查是否有对应工厂的订阅用户
    SELECT COUNT(*) INTO user_subscription_count
    FROM user_subscriptions 
    WHERE factory_name = NEW.receiver 
    AND template_id = 'pe6RVTNTaFPvBv3BGU-mYi8VV9agbvsc27aGpQtR9c8' 
    AND status = 1;
    
    -- 如果没有发送过通知且有订阅用户，则插入通知任务
    IF notification_count = 0 AND user_subscription_count > 0 THEN
        -- 插入通知任务到队列表
        INSERT INTO notification_queue (
            order_no, 
            factory_name, 
            notification_type, 
            status, 
            created_at
        ) VALUES (
            NEW.order_no, 
            NEW.receiver, 
            'order_created', 
            'pending', 
            NOW()
        );
    END IF;
    
END$$

DELIMITER ;

-- 创建通知队列表（如果不存在）
CREATE TABLE IF NOT EXISTS notification_queue (
    id INT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    order_no VARCHAR(100) NOT NULL COMMENT '订单号',
    factory_name VARCHAR(100) NOT NULL COMMENT '工厂名称',
    notification_type VARCHAR(50) NOT NULL COMMENT '通知类型：order_created, order_shipped等',
    status VARCHAR(20) DEFAULT 'pending' COMMENT '状态：pending, processing, completed, failed',
    retry_count INT DEFAULT 0 COMMENT '重试次数',
    error_msg TEXT COMMENT '错误信息',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_status (status),
    INDEX idx_order_no (order_no),
    INDEX idx_factory_name (factory_name),
    INDEX idx_notification_type (notification_type),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='通知队列表';

-- 查看触发器是否创建成功
SHOW TRIGGERS LIKE 'shipping_detail';

-- 查看通知队列表结构
DESCRIBE notification_queue;

-- 测试数据（可选，用于验证触发器）
-- INSERT INTO shipping_detail (order_no, receiver, follower, notice_date, deliver_company, product_name, spec, width, weight, quantity, remark)
-- VALUES ('TEST_ORDER_002', '测试工厂', '测试处理人', NOW(), '测试物流', '测试产品', '测试规格', 100, 50, 10, '测试备注');

-- 查看通知队列数据
-- SELECT * FROM notification_queue ORDER BY created_at DESC LIMIT 5;
