/**
 * 路由索引文件
 * 统一管理所有API路由
 */
const express = require('express');
const router = express.Router();

// 导入各个路由模块
const healthRouter = require('./health');
const historyRouter = require('./history');
const uploadRouter = require('./upload');
const recognizeRouter = require('./recognize');
const registrationFormRouter = require('./registrationForm');
const cartonInfoRouter = require('./cartonInfo');
const authRouter = require('./auth');
const ordersRouter = require('./orders');
const imagesRouter = require('./images');
const dbMonitorRouter = require('./dbMonitor');
const subscriptionRouter = require('./subscription');
const notificationsRouter = require('./notifications');

// 注册路由
router.use('/health', healthRouter);
router.use('/history', historyRouter);
router.use('/upload', uploadRouter);
router.use('/recognize', recognizeRouter);
router.use('/registration-form', registrationFormRouter);
router.use('/carton-info', cartonInfoRouter);
router.use('/auth', authRouter);
router.use('/orders', ordersRouter);
router.use('/images', imagesRouter);
router.use('/db-monitor', dbMonitorRouter);
router.use('/subscription', subscriptionRouter);
router.use('/notifications', notificationsRouter);

// API根路径信息
router.get('/', (req, res) => {
    res.json({
        name: '订单图片管理系统API服务',
        version: '2.0.0',
        description: '提供用户认证、订单管理、图片上传、文字识别、表格识别和历史记录功能',
        endpoints: {
            health: {
                path: '/api/health',
                method: 'GET',
                description: '健康检查'
            },
            historyToday: {
                path: '/api/history/today',
                method: 'GET',
                description: '查询今日上传的图片记录'
            },
            historyImage: {
                path: '/api/history/image/*',
                method: 'GET',
                description: '获取图片文件预览'
            },
            upload: {
                path: '/api/upload',
                method: 'POST',
                description: '上传图片文件',
                contentType: 'multipart/form-data',
                parameters: {
                    image: '图片文件 (支持jpg, png, gif, bmp, webp，最大10MB)'
                }
            },
            uploadInfo: {
                path: '/api/upload/:filename',
                method: 'GET',
                description: '获取已上传文件信息'
            },
            uploadDelete: {
                path: '/api/upload/:filename',
                method: 'DELETE',
                description: '删除已上传的文件'
            },
            recognize: {
                path: '/api/recognize',
                method: 'POST',
                description: '图片识别（智能判断文字/表格类型）',
                contentType: 'multipart/form-data',
                parameters: {
                    image: '图片文件 (支持jpg, png, gif, bmp, webp，最大10MB)'
                }
            },
            registrationFormRecognize: {
                path: '/api/registration-form/recognize',
                method: 'POST',
                description: '装柜放船样登记表识别',
                contentType: 'multipart/form-data',
                parameters: {
                    image: '装柜放船样登记表图片文件'
                }
            },
            registrationFormSave: {
                path: '/api/registration-form/save',
                method: 'POST',
                description: '保存确认后的装柜放船样登记表数据',
                contentType: 'application/json',
                parameters: {
                    data: '确认后的表格数据数组',
                    imageInfo: '图片信息'
                }
            },
            cartonInfoRecognize: {
                path: '/api/carton-info/recognize',
                method: 'POST',
                description: '装柜信息表识别',
                contentType: 'multipart/form-data',
                parameters: {
                    image: '装柜信息表图片文件'
                }
            },
            cartonInfoSave: {
                path: '/api/carton-info/save',
                method: 'POST',
                description: '保存确认后的装柜信息表数据',
                contentType: 'application/json',
                parameters: {
                    data: '确认后的装柜信息数据',
                    imageInfo: '图片信息'
                }
            },
            // 新增订单管理API
            authLogin: {
                path: '/api/auth/login',
                method: 'POST',
                description: '用户登录',
                contentType: 'application/json',
                parameters: {
                    username: '用户名',
                    password: '密码'
                }
            },
            authProfile: {
                path: '/api/auth/profile',
                method: 'GET',
                description: '获取用户信息',
                headers: {
                    Authorization: 'Bearer token'
                }
            },
            ordersHistory: {
                path: '/api/orders/history',
                method: 'GET',
                description: '获取订单历史列表',
                headers: {
                    Authorization: 'Bearer token'
                }
            },
            ordersStats: {
                path: '/api/orders/stats',
                method: 'GET',
                description: '获取订单统计信息',
                headers: {
                    Authorization: 'Bearer token'
                }
            },
            imagesUpload: {
                path: '/api/images/upload',
                method: 'POST',
                description: '上传图片到指定订单',
                contentType: 'multipart/form-data',
                headers: {
                    Authorization: 'Bearer token'
                },
                parameters: {
                    file: '图片文件',
                    order_number: '订单号',
                    factory_name: '工厂名称'
                }
            },
            imagesOrderList: {
                path: '/api/images/order/:orderNumber',
                method: 'GET',
                description: '获取指定订单的图片列表',
                headers: {
                    Authorization: 'Bearer token'
                }
            },
            imagesDelete: {
                path: '/api/images/delete',
                method: 'POST',
                description: '批量删除图片',
                contentType: 'application/json',
                headers: {
                    Authorization: 'Bearer token'
                },
                parameters: {
                    imageIds: '图片ID数组'
                }
            },
            dbMonitor: {
                path: '/api/db-monitor',
                method: 'GET',
                description: '获取数据库连接池监控信息'
            },
            dbStatus: {
                path: '/api/db-monitor/status',
                method: 'GET',
                description: '获取数据库连接池状态'
            },
            dbHealth: {
                path: '/api/db-monitor/health',
                method: 'GET',
                description: '数据库健康检查'
            },
            dbMetrics: {
                path: '/api/db-monitor/metrics',
                method: 'GET',
                description: '获取数据库性能指标'
            },
            dbResetStats: {
                path: '/api/db-monitor/reset-stats',
                method: 'POST',
                description: '重置连接池统计信息'
            },
            dbCleanup: {
                path: '/api/db-monitor/cleanup',
                method: 'POST',
                description: '清理空闲连接'
            }
        },
        features: [
            '用户认证与权限管理',
            '工厂多用户支持',
            '订单管理系统',
            '图片上传与管理',
            '智能图片类型检测（文字/表格）',
            '高精度文字识别',
            '专业表格识别',
            '装柜放船样登记表专用识别',
            '装柜信息表专用识别',
            '用户确认后数据库存储',
            '识别结果数据库存储',
            '历史记录查询',
            '文件上传管理',
            '图片批量删除',
            '订单统计分析',
            '完善的错误处理',
            '数据库连接池监控',
            '性能指标统计',
            '健康检查机制',
            '连接池优化管理'
        ],
        timestamp: new Date().toISOString()
    });
});

module.exports = router;
