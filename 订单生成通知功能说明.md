# 订单生成通知功能说明

## 功能概述

本功能为微信小程序新增了订单生成通知功能，当`shipping_detail`表有新增数据时，系统会自动检测并向订阅用户发送"订单已生成"的订阅消息提醒。

## 核心特性

### 1. 自动触发机制
- 使用MySQL触发器检测`shipping_detail`表的新增数据
- 自动判断是否需要发送通知（防重复发送）
- 检查是否有对应工厂的订阅用户

### 2. 通知队列系统
- 异步处理通知发送，不影响主业务流程
- 支持失败重试机制（最多3次）
- 完整的状态跟踪：pending、processing、completed、failed

### 3. HTTPS协议支持
- 所有微信API调用均使用HTTPS协议
- 增强安全性和兼容性

### 4. 管理功能
- 通知队列状态监控
- 手动重试失败通知
- 定时清理已完成任务

## 技术架构

### 数据库层
```sql
-- 通知队列表
notification_queue (
    id, order_no, factory_name, notification_type, 
    status, retry_count, error_msg, created_at, updated_at
)

-- 触发器
tr_shipping_detail_insert: 监听shipping_detail表INSERT操作
```

### 服务层
```javascript
// 核心服务类
NotificationQueueService: 通知队列处理服务
SubscriptionService: 订阅消息服务（新增订单生成通知方法）
WechatService: 微信API服务（HTTPS协议支持）
```

### API层
```
/api/notifications/stats - 获取通知统计
/api/notifications/queue - 获取通知队列
/api/notifications/retry/:id - 重试失败通知
/api/notifications/send-order-created - 手动发送通知
/api/notifications/cleanup - 清理已完成通知
```

## 部署步骤

### 1. 数据库初始化
```bash
# 执行部署脚本
mysql -u mls01 -p identify < server/scripts/deploy_notification_system.sql
```

### 2. 安装依赖
```bash
cd server
npm install node-cron
```

### 3. 启动服务
```bash
# 开发环境
npm run dev

# 生产环境
npm start
```

## 配置说明

### 微信小程序配置
- **模板ID**: `pe6RVTNTaFPvBv3BGU-mYi8VV9agbvsc27aGpQtR9c8`
- **消息内容**:
  - `character_string1`: 订单号
  - `thing2`: 客户名称（工厂名称）
  - `thing11`: 当前处理人
  - `phrase13`: 订单状态（"订单已生成"）
  - `time31`: 下单时间

### 触发条件
1. `shipping_detail`表有新增记录
2. 对应工厂有订阅用户
3. 该订单未发送过生成通知

## 使用流程

### 1. 用户订阅
用户在小程序中申请订阅消息权限，系统保存订阅信息到`user_subscriptions`表。

### 2. 订单生成
当有新订单数据插入`shipping_detail`表时：
1. 触发器自动检测
2. 判断是否满足发送条件
3. 插入通知任务到队列

### 3. 通知发送
通知队列服务：
1. 每5秒检查一次待处理任务
2. 异步发送订阅消息
3. 记录发送结果和状态

### 4. 状态管理
- 发送成功：标记为completed
- 发送失败：增加重试次数，最多重试3次
- 超过重试次数：标记为failed

## 监控和维护

### 1. 状态监控
```javascript
// 获取队列统计
GET /api/notifications/stats

// 响应示例
{
  "success": true,
  "data": {
    "pending": 2,
    "processing": 0,
    "completed": 15,
    "failed": 1
  }
}
```

### 2. 手动干预
```javascript
// 重试失败的通知
POST /api/notifications/retry/123

// 手动发送通知
POST /api/notifications/send-order-created
{
  "orderNo": "ORDER_001",
  "factoryName": "测试工厂"
}
```

### 3. 定时清理
系统每天凌晨2点自动清理7天前的已完成通知记录。

## 错误处理

### 常见错误及解决方案

1. **用户未订阅**
   - 错误：`工厂未找到订阅用户`
   - 解决：用户需要在小程序中申请订阅权限

2. **订单信息不存在**
   - 错误：`未找到订单信息`
   - 解决：检查`shipping_detail`表数据完整性

3. **微信API调用失败**
   - 错误：`access_token失效`或`网络超时`
   - 解决：系统会自动重试，检查网络连接

4. **重复发送**
   - 系统自动防重复，同一订单只发送一次生成通知

## 性能优化

### 1. 异步处理
- 通知发送不阻塞主业务流程
- 使用队列机制平滑处理高并发

### 2. 批量处理
- 每次最多处理10个通知任务
- 避免系统资源过度占用

### 3. 连接复用
- 微信API调用使用HTTPS连接池
- access_token自动缓存和刷新

## 安全考虑

### 1. HTTPS协议
- 所有微信API调用强制使用HTTPS
- 证书验证确保通信安全

### 2. 防重复发送
- 基于订单号和通知类型的唯一性检查
- 避免用户收到重复消息

### 3. 错误信息保护
- 敏感信息不记录到错误日志
- 用户openid部分隐藏显示

## 扩展性

### 1. 多种通知类型
当前支持`order_created`，可轻松扩展：
- `order_shipped`: 订单发货通知
- `order_delivered`: 订单送达通知
- `order_cancelled`: 订单取消通知

### 2. 多模板支持
可配置不同的微信消息模板ID，支持不同业务场景。

### 3. 条件过滤
可在触发器中添加更复杂的业务逻辑判断。

## 注意事项

1. **微信限制**
   - 用户必须主动订阅才能接收消息
   - 每次订阅只能发送一次消息
   - 需要在用户操作后及时申请订阅权限

2. **数据库性能**
   - 触发器会增加INSERT操作的开销
   - 建议在低峰期部署

3. **网络依赖**
   - 依赖微信API服务可用性
   - 建议配置网络监控

4. **存储空间**
   - 通知队列和消息日志会占用存储空间
   - 定期清理历史数据
