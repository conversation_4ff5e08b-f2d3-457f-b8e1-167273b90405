# 微信小程序订阅消息功能说明

## 功能概述

本系统已集成微信小程序订阅消息功能，当坯布商在小程序中上传订单图片时，系统会自动向已订阅的用户发送消息通知。

## 配置信息

- **小程序AppID**: `wx803f25e0b6de779b`
- **小程序AppSecret**: `effe9d3b781457b6877b9c7faf175fd7`
- **消息模板ID**: `pe6RVTNTaFPvBv3BGU-mYi8VV9agbvsc27aGpQtR9c8`
- **模板类型**: 订单进度提醒

## 消息模板内容

```
订单进度提醒

订单号: {{character_string1.DATA}}
客户名称: {{thing2.DATA}}
当前处理人: {{thing11.DATA}}
订单状态: {{phrase13.DATA}}
下单时间: {{time31.DATA}}
```

## 数据库表结构

### 1. 用户订阅表 (user_subscriptions)

```sql
CREATE TABLE user_subscriptions (
    id INT PRIMARY KEY AUTO_INCREMENT,
    openid VARCHAR(100) NOT NULL,
    template_id VARCHAR(100) NOT NULL,
    username VARCHAR(50),
    factory_name VARCHAR(100),
    subscribe_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    status TINYINT DEFAULT 1,
    INDEX idx_openid (openid),
    UNIQUE KEY uk_openid_template (openid, template_id)
);
```

### 2. 消息发送记录表 (message_logs)

```sql
CREATE TABLE message_logs (
    id INT PRIMARY KEY AUTO_INCREMENT,
    openid VARCHAR(100) NOT NULL,
    template_id VARCHAR(100) NOT NULL,
    message_content TEXT,
    send_status TINYINT DEFAULT 0,
    error_msg VARCHAR(500),
    send_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_openid (openid),
    INDEX idx_send_status (send_status)
);
```

## 实现流程

### 1. 用户订阅流程

1. 用户登录成功后，系统自动提示订阅消息
2. 用户同意订阅后，前端获取用户openid
3. 将订阅信息保存到数据库

### 2. 消息发送流程

1. 用户上传订单图片
2. 系统检查该订单是否已发送过通知
3. 如果未发送，查询用户订阅状态
4. 获取订单信息并构建消息内容
5. 调用微信API发送订阅消息
6. 记录发送结果到数据库

### 3. 防重复发送机制

- 每个订单只发送一次通知
- 通过检查 `message_logs` 表中的历史记录实现
- 搜索条件：模板ID + 订单号

## API接口

### 1. 获取用户openid
```
POST /api/subscription/get-openid
Body: { "code": "微信登录code" }
```

### 2. 保存订阅状态
```
POST /api/subscription/save
Headers: Authorization: Bearer <token>
Body: { 
    "subscribeResult": { "template_id": "accept" },
    "openid": "用户openid"
}
```

### 3. 检查订阅状态
```
GET /api/subscription/status
Headers: Authorization: Bearer <token>
```

### 4. 发送测试通知
```
POST /api/subscription/send-notification
Headers: Authorization: Bearer <token>
Body: { "orderNo": "订单号" }
```

### 5. 获取消息历史
```
GET /api/subscription/message-history?page=1&limit=20
Headers: Authorization: Bearer <token>
```

## 前端使用方法

### 1. 导入订阅管理器
```javascript
import subscriptionManager from '../../utils/subscriptionManager.js';
```

### 2. 申请订阅权限
```javascript
await subscriptionManager.requestSubscribeMessage();
```

### 3. 检查订阅状态
```javascript
const isSubscribed = await subscriptionManager.checkSubscriptionStatus();
```

### 4. 发送测试通知
```javascript
await subscriptionManager.sendTestNotification('订单号');
```

## 测试页面

访问 `pages/subscriptionTest/subscriptionTest` 页面可以进行功能测试：

- 申请订阅权限
- 检查订阅状态
- 发送测试通知
- 查看消息历史
- 调试功能

## 部署说明

### 1. 数据库初始化
```bash
# 执行SQL脚本创建表
mysql -u mls01 -p identify < server/scripts/init_subscription_tables.sql
```

### 2. 安装依赖
```bash
cd server
npm install axios
```

### 3. 微信小程序后台配置
- 在微信小程序后台配置服务器域名白名单
- 申请订阅消息模板
- 配置消息推送权限

## 注意事项

1. **订阅消息限制**
   - 用户必须主动订阅才能接收消息
   - 每次订阅只能发送一次消息
   - 需要在用户操作后及时申请订阅权限

2. **消息内容规范**
   - 严格按照模板格式发送
   - 不能发送营销类消息
   - 内容要与业务相关

3. **错误处理**
   - 发送失败会记录错误信息
   - 支持查看发送历史和状态
   - 异步发送不影响主业务流程

4. **性能优化**
   - 消息发送采用异步处理
   - 防重复发送机制
   - access_token自动缓存和刷新

## 故障排查

### 1. 消息发送失败
- 检查用户是否已订阅
- 确认模板ID是否正确
- 查看错误日志获取具体原因

### 2. 获取openid失败
- 确认小程序AppID和AppSecret配置正确
- 检查微信登录code是否有效
- 确认网络连接正常

### 3. 订阅申请失败
- 确认在微信小程序环境中运行
- 检查模板ID是否正确
- 确认用户操作触发时机合适

## 监控和日志

系统会自动记录以下信息：
- 订阅申请记录
- 消息发送成功/失败记录
- 错误详细信息
- 发送时间和状态

可通过数据库查询或API接口获取相关统计信息。
