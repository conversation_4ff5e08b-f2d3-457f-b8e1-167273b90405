/**
 * 图片文字识别小程序后端服务器
 * 基于Express.js，支持图片上传和OCR识别
 * 重构版本 - 使用模块化路由结构
 */

// 加载环境变量
// 优先加载.env.local（本地开发），然后是.env（生产环境）
require('dotenv').config({ path: '.env.local' });
require('dotenv').config();

const express = require('express');
const cors = require('cors');
const path = require('path');
const fs = require('fs');

// 导入数据库配置
const database = require('./config/database');

// 导入中间件
const { requestLogger, errorLogger } = require('./middleware/logger');
const { globalErrorHandler, notFoundHandler } = require('./middleware/errorHandler');
const { dbMonitorMiddleware } = require('./middleware/dbMonitor');

// 导入路由
const apiRoutes = require('./routes/index');

// 导入通知队列服务
const NotificationQueueService = require('./utils/notificationQueueService');

const app = express();
const PORT = process.env.PORT || 3000;

// 信任nginx代理
app.set('trust proxy', 1);

// 基础中间件配置
app.use(cors({
    origin: [
        'https://www.mls2005.top',
        'http://localhost:3000',
        'http://************:3000',
        'https://************'
    ],
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With']
})); // 配置跨域请求
app.use(express.json({ limit: '50mb' })); // 解析JSON请求体
app.use(express.urlencoded({ extended: true, limit: '50mb' })); // 解析URL编码请求体

// 请求日志中间件
app.use(requestLogger);

// 数据库监控中间件
app.use(dbMonitorMiddleware);

// 创建上传目录 - 使用C盘临时上传目录
const uploadDir = 'C:\\Warehouse_Img\\temp_uploads';
if (!fs.existsSync(uploadDir)) {
    fs.mkdirSync(uploadDir, { recursive: true });
}

// 静态文件服务 - 提供仓库图片访问（仓库管理系统）
const warehouseImgDir = 'C:\\MLS\\Warehouse_Img';
if (!fs.existsSync(warehouseImgDir)) {
    fs.mkdirSync(warehouseImgDir, { recursive: true });
}
app.use('/warehouse-images', express.static(warehouseImgDir));

// 静态文件服务 - 提供订单图片访问（订单图片管理系统）
const orderImgDir = 'C:\\MLS\\Order_Img';
if (!fs.existsSync(orderImgDir)) {
    fs.mkdirSync(orderImgDir, { recursive: true });
}
app.use('/order-images', express.static(orderImgDir));

// 静态文件服务 - 提供测试页面访问
app.use(express.static(__dirname));

// 注册API路由
app.use('/api', apiRoutes);

// 错误处理中间件
app.use(errorLogger);
app.use(globalErrorHandler);

// 404处理中间件
app.use(notFoundHandler);


// 启动HTTP服务器 - nginx将处理SSL终止并代理到此服务器
const server = app.listen(PORT, '0.0.0.0', async () => {
    console.log(`🚀 HTTP服务器启动成功 (nginx HTTPS代理模式):`);
    console.log(`   - 内部访问: http://localhost:${PORT}`);
    console.log(`   - HTTPS域名: https://www.mls2005.top -> nginx -> http://localhost:${PORT}`);
    console.log(`   - HTTPS IP: https://************ -> nginx -> http://localhost:${PORT}`);
    console.log(`   - 环境: ${process.env.NODE_ENV || 'development'}`);

    // 测试数据库连接（非阻塞）
    try {
        const dbConnected = await database.testConnection();
        if (dbConnected) {
            console.log('✅ 数据库连接成功');

            // 启动通知队列服务
            try {
                const notificationService = new NotificationQueueService();
                notificationService.start();
                console.log('🔔 通知队列服务已启动');

                // 设置定时清理任务（每天凌晨2点）
                const schedule = require('node-cron');
                schedule.schedule('0 2 * * *', () => {
                    notificationService.cleanupCompletedNotifications();
                });
                console.log('🧹 定时清理任务已设置（每天凌晨2点）');

            } catch (error) {
                console.error('❌ 启动通知队列服务失败:', error.message);
            }
        } else {
            console.log('⚠️  数据库连接失败，但服务器继续运行');
        }
    } catch (error) {
        console.log('⚠️  数据库连接测试失败:', error.message);
        console.log('   服务器继续运行，但数据库功能可能不可用');
    }
});

// 设置服务器超时时间（5分钟）
server.timeout = 300000; // 5分钟
server.keepAliveTimeout = 65000; // 65秒
server.headersTimeout = 66000; // 66秒

console.log(`⚙️  服务器超时配置:`);
console.log(`   - 请求超时: ${server.timeout / 1000}秒`);
console.log(`   - Keep-Alive超时: ${server.keepAliveTimeout / 1000}秒`);
console.log(`   - Headers超时: ${server.headersTimeout / 1000}秒`);

// 注意：HTTP重定向服务器已被注释，因为端口80可能被nginx占用
// 如果需要HTTP重定向，请在nginx中配置
// 或者使用不同的端口，如8080

module.exports = app;
